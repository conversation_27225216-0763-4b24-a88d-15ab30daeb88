[project]
name = "ocr"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.12.13",
    "azure-ai-documentintelligence>=1.0.2",
    "httpx[socks]>=0.28.1",
    "marker-pdf>=1.7.5",
    "pproxy>=2.7.9",
    "psutil>=7.0.0",
    "pysocks>=1.7.1",
    "python-dotenv>=1.1.0",
    "requests[socks]>=2.32.4",
    "torchvision>=0.22.1",
]

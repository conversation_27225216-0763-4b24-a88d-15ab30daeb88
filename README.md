# OCR Comparison Project

This project compares different OCR models and services for PDF document processing, including marker-pdf with various LLM backends and Azure Document Intelligence.

## Features

- Compare multiple OCR models:
  - Gemini 2.5 Flash (via marker-pdf)
  - OpenRouter Deepseek (via marker-pdf)
  - Azure Document Intelligence
- Enhanced proxy management for API access
- Detailed result comparison and analysis
- Markdown output format with image extraction
- Configurable batch processing and language support
- Async processing for Azure Document Intelligence

## Setup

1. Install dependencies:
```bash
uv sync
```

2. Configure environment variables in `.env`:
```bash
# Copy and modify the .env file with your API keys
```

3. Run the comparison:
```bash
uv run python main.py
```

## Configuration

The project uses environment variables for configuration. See `.env` file for available options:

### Marker-PDF Configuration
- `GEMINI_API_KEY`: Your Google Gemini API key
- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `NORDVPN_PROXY`: SOCKS5 proxy configuration (if needed)
- Various marker-pdf configuration options

### Azure Document Intelligence Configuration
- `AZURE_DOCINTEL_ENDPOINT`: Your Azure Document Intelligence endpoint
- `AZURE_DOCINTEL_KEY`: Your Azure Document Intelligence key
- `AZURE_MODEL_ID`: Model ID to use (default: prebuilt-layout)
- `AZURE_LOCALE`: Language locale (default: en-US)

## Output

Results are saved in the `ocr_comparison_results/` directory with:
- Individual model folders containing OCR results and extracted images
- Comparison summary reports in Markdown format
- Detailed configuration and performance metrics

## Models Supported

1. **Gemini 2.5 Flash**: Google's latest multimodal model via marker-pdf
2. **OpenRouter Deepseek R1**: Advanced reasoning model via OpenRouter and marker-pdf
3. **Azure Document Intelligence**: Microsoft's document analysis service with advanced OCR capabilities

## Key Files

- `main.py`: Main comparison script that runs all OCR models
- `marker_ocr_socks5_proxy.py`: Marker-PDF integration with Gemini and OpenRouter
- `azure_ocr.py`: Azure Document Intelligence integration
- `proxy_manager.py`: Enhanced proxy management utilities

## Azure Document Intelligence Features

The Azure integration includes:
- High-resolution OCR capabilities
- Math formula recognition (MATH_SOLVER feature)
- Multi-language support with locale-specific optimization
- Async processing for better performance
- Structured markdown output
- Error handling and retry mechanisms

## Proxy Support

The project includes enhanced proxy management for accessing APIs that may be geo-restricted, particularly useful for Gemini API access.

## Usage Examples

### Basic OCR Comparison
```bash
uv run python main.py
```

### Test Azure Document Intelligence Only
```bash
uv run python azure_ocr.py
```

### Batch Processing with Azure
```python
import asyncio
from azure_ocr import batch_analyze_documents

documents = [
    ("document1.pdf", "en-US"),
    ("document2.pdf", "zh-Hant"),
]

results = asyncio.run(batch_analyze_documents(documents))
```
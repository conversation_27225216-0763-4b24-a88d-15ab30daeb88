import asyncio
from markerpdf.unified_ocr import (
    create_gemini_config_with_http_proxy,
    create_openrouter_config,
    test_model_with_retry,
    save_results_to_markdown
)
from azure_ocr import test_azure_ocr

def main():
    """Main function: Execute OCR model comparison test"""
    print("Starting OCR model comparison test")
    print("Test models: Gemini 2.5 Flash (HTTP Proxy) vs OpenRouter Deepseek vs Azure Document AI")
    print("Using unified OCR architecture with HTTP proxy as default")

    results = []

    # Test Gemini model with HTTP proxy (default configuration)
    print(f"\n{'='*50}")
    print("Testing Gemini with HTTP proxy (default configuration)")
    print(f"{'='*50}")
    gemini_config = create_gemini_config_with_http_proxy()
    gemini_result = test_model_with_retry(gemini_config, "Gemini 2.5 Flash (HTTP Proxy)")
    results.append(gemini_result)

    # Test OpenRouter Deepseek model
    print(f"\n{'='*50}")
    print("Testing OpenRouter Deepseek")
    print(f"{'='*50}")
    openrouter_config = create_openrouter_config()
    deepseek_result = test_model_with_retry(openrouter_config, "OpenRouter Deepseek R1")
    results.append(deepseek_result)

    # Test Azure Document AI model
    print(f"\n{'='*50}")
    print("Starting Azure Document AI test")
    print(f"{'='*50}")
    azure_result = asyncio.run(test_azure_ocr())
    results.append(azure_result)

    # Save results to Markdown file
    save_results_to_markdown(results, "ocr_comparison_results.md")

    print(f"\n{'='*50}")
    print("Test completed!")
    print("Detailed comparison report saved to: ocr_comparison_results.md")
    print(f"{'='*50}")

    # Print summary
    print("\n📊 Test Summary:")
    for result in results:
        status = "✅ Success" if result['success'] else "❌ Failed"
        processing_time = result.get('processing_time', 0)
        print(f"  - {result['model_name']}: {status} ({processing_time:.2f}s)")

    print(f"\n🎯 Default configuration: HTTP Proxy + Gemini")
    print(f"📁 All results saved in respective output folders")

if __name__ == "__main__":
    main()
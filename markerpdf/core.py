#!/usr/bin/env python3
"""
Unified base OCR class for marker-pdf functionality

This module provides a unified architecture for OCR processing that eliminates
code duplication between different proxy implementations while maintaining
flexibility for different LLM services and proxy configurations.
"""

import os
import time
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List

# Early proxy setup before any other imports
from dotenv import load_dotenv
load_dotenv()

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered
from .proxy_manager import ProxyManager


class BaseMarkerOCR(ABC):
    """
    統一的 marker-pdf OCR 基礎類別
    
    這個基礎類別處理核心的 marker-pdf 功能，消除不同代理實現之間的代碼重複。
    子類別只需要實現特定的代理設置和 LLM 服務配置。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化 OCR 處理器
        
        Args:
            config: 配置對象，包含 LLM 服務和代理設置
        """
        self.config = config
        self.proxy_manager = ProxyManager()
        self._setup_proxy()
    
    @abstractmethod
    def _setup_proxy(self) -> None:
        """設置代理配置 - 由子類別實現"""
        pass
    
    @abstractmethod
    def get_model_name(self) -> str:
        """獲取模型名稱 - 由子類別實現"""
        pass
    
    def _check_proxy(self) -> bool:
        """檢查代理設置是否正確"""
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        current_proxy = None
        
        for var in proxy_vars:
            if var in os.environ:
                current_proxy = os.environ[var]
                break
        
        if current_proxy:
            print(f"Current proxy: {current_proxy}")
            return True
        else:
            print("No proxy detected")
            return False
    
    def _create_converter(self, config_dict: Dict[str, Any]) -> PdfConverter:
        """
        創建 PDF 轉換器
        
        Args:
            config_dict: 配置字典
            
        Returns:
            配置好的 PdfConverter 實例
        """
        config_parser = ConfigParser(config_dict)
        return PdfConverter(
            config=config_parser.generate_config_dict(),
            artifact_dict=create_model_dict(),
            processor_list=config_parser.get_processors(),
            renderer=config_parser.get_renderer(),
            llm_service=config_parser.get_llm_service()
        )
    
    def _process_pdf(self, converter: PdfConverter, pdf_path: str) -> Tuple[str, Dict, List]:
        """
        處理 PDF 文件
        
        Args:
            converter: PDF 轉換器
            pdf_path: PDF 文件路徑
            
        Returns:
            元組包含 (文本內容, 元數據, 圖片列表)
        """
        print(f"📄 Processing PDF: {pdf_path}")
        rendered = converter(pdf_path)
        text, metadata, images = text_from_rendered(rendered)
        return text, metadata, images
    
    def save_model_output(self, text: str, images: Dict, model_name: str, 
                         config: Dict[str, Any], timestamp: str) -> str:
        """
        保存模型輸出結果
        
        Args:
            text: 提取的文本
            images: 提取的圖片
            model_name: 模型名稱
            config: 配置信息
            timestamp: 時間戳
            
        Returns:
            輸出文件夾路徑
        """
        # 創建輸出文件夾
        safe_model_name = model_name.replace(" ", "_").replace("(", "").replace(")", "")
        output_folder = f"output_{safe_model_name}_{timestamp}"
        Path(output_folder).mkdir(exist_ok=True)
        
        # 保存提取的文本
        text_file = Path(output_folder) / "extracted_text.md"
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(f"# OCR Results - {model_name}\n\n")
            f.write(f"**Processing Time:** {timestamp}\n\n")
            f.write("**Configuration:**\n")
            for key, value in config.items():
                if key != 'gemini_api_key':  # 不記錄敏感信息
                    f.write(f"- {key}: {value}\n")
            f.write("\n**Extracted Text:**\n\n")
            f.write(text)
        
        # 保存圖片
        if images:
            images_folder = Path(output_folder) / "images"
            images_folder.mkdir(exist_ok=True)
            
            for i, (page_num, image_data) in enumerate(images.items()):
                if hasattr(image_data, 'save'):
                    image_path = images_folder / f"page_{page_num}_image_{i}.png"
                    image_data.save(image_path)
        
        print(f"📁 Results saved to: {output_folder}")
        return output_folder
    
    def test_model_with_retry(self, pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
        """
        使用重試機制測試模型
        
        Args:
            pdf_path: PDF 文件路徑
            max_retries: 最大重試次數
            
        Returns:
            包含測試結果的字典
        """
        model_name = self.get_model_name()
        config_dict = self.config
        error_message = "Unknown error occurred"
        
        print(f"\n{'='*50}")
        print(f"Testing {model_name}")
        print(f"{'='*50}")
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    print(f"\nAttempt {attempt + 1}...")
                    # 重新檢查和設置代理
                    if self._check_proxy():
                        print("Proxy has been reset")
                    
                    # 如果是 Gemini 模型，確保代理正確設置
                    if "Gemini" in model_name:
                        self._setup_proxy()
                        print("Forced proxy reset for Gemini")
                
                # 創建轉換器
                converter = self._create_converter(config_dict)
                
                # 記錄開始時間
                start_time = time.time()
                print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 執行 OCR 轉換
                try:
                    text, metadata, images = self._process_pdf(converter, pdf_path)
                except Exception as inner_e:
                    # 檢查是否為超時錯誤
                    error_str = str(inner_e).lower()
                    if any(timeout_keyword in error_str for timeout_keyword in 
                          ['timeout', 'timed out', 'time out', 'connection', 'read operation']):
                        print(f"⏰ Detected timeout error: {inner_e}")
                        if attempt < max_retries - 1:
                            print(f"Will retry with longer timeout...")
                            continue
                    raise inner_e
                
                # 計算處理時間
                end_time = time.time()
                processing_time = end_time - start_time
                
                print(f"✅ Processing completed successfully!")
                print(f"⏱️ Processing time: {processing_time:.2f} seconds")
                print(f"📄 Text length: {len(text)} characters")
                print(f"🖼️ Images extracted: {len(images)}")
                
                # 保存結果
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_folder = self.save_model_output(text, images, model_name, config_dict, timestamp)
                
                return {
                    'model_name': model_name,
                    'processing_time': processing_time,
                    'text_length': len(text),
                    'image_count': len(images),
                    'text_content': text,
                    'images': images,
                    'output_folder': output_folder,
                    'config': config_dict,
                    'success': True,
                    'error': None,
                    'attempts': attempt + 1
                }
                
            except Exception as e:
                error_message = str(e)
                print(f"Attempt {attempt + 1} failed: {error_message}")
                
                # 檢查是否為地理位置限制錯誤
                if "FAILED_PRECONDITION" in error_message and "location is not supported" in error_message:
                    print("⚠️ Detected geolocation restriction error, this may be a proxy configuration issue")
                    
                    # 如果還有重試機會，等待後重試
                    if attempt < max_retries - 1:
                        wait_time = (attempt + 1) * 5  # 遞增等待時間
                        print(f"Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                        continue
                else:
                    # 非代理相關錯誤，立即失敗
                    break
        
        # 所有重試都失敗
        print(f"\n❌ All {max_retries} attempts failed")
        return {
            'model_name': model_name,
            'processing_time': 0,
            'text_length': 0,
            'image_count': 0,
            'text_content': '',
            'images': {},
            'output_folder': None,
            'config': config_dict,
            'success': False,
            'error': error_message,
            'attempts': max_retries
        }
    
    def test_model(self, pdf_path: str = "sample.pdf") -> Dict[str, Any]:
        """測試模型（保持接口兼容性）"""
        return self.test_model_with_retry(pdf_path)


def save_results_to_markdown(results: List[Dict[str, Any]], output_file: str = "comparison_results.md") -> None:
    """
    將結果保存為 Markdown 格式
    
    Args:
        results: 測試結果列表
        output_file: 輸出文件名
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# OCR Performance Comparison Results\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for result in results:
            f.write(f"## {result['model_name']}\n\n")
            f.write(f"- **Success:** {'✅' if result['success'] else '❌'}\n")
            f.write(f"- **Processing Time:** {result['processing_time']:.2f} seconds\n")
            f.write(f"- **Text Length:** {result['text_length']} characters\n")
            f.write(f"- **Images:** {result['image_count']}\n")
            f.write(f"- **Attempts:** {result['attempts']}\n")
            
            if not result['success']:
                f.write(f"- **Error:** {result['error']}\n")
            
            if result['output_folder']:
                f.write(f"- **Output Folder:** {result['output_folder']}\n")
            
            f.write("\n")
    
    print(f"📄 Comparison results saved to: {output_file}")

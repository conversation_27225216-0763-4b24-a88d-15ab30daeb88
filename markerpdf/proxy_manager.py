import os
import sys
import urllib.request
import subprocess
import threading
import time
from contextlib import contextmanager
from typing import Optional, Dict, Any


class ProxyType:
    """代理類型常量"""
    NONE = "none"
    HTTP = "http"
    SOCKS5 = "socks5"
    AUTO = "auto"


class ProxyManager:
    """Proxy manager to ensure consistency and reliability of proxy settings"""
    
    def __init__(self):
        self.original_env = {}
        self.proxy_process = None  # 用於存儲 pproxy 進程
        self.http_proxy_port = 8888  # 本地 HTTP 代理端口
        self.proxy_vars = [
            'HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy',
            'ALL_PROXY', 'all_proxy', 'NO_PROXY', 'no_proxy',
            # Add possible NordVPN related variables
            'NORDVPN_PROXY', 'nordvpn_proxy', 'NORDVPN',
            # Add other possible proxy variables
            'FTP_PROXY', 'ftp_proxy', 'SOCKS_PROXY', 'socks_proxy'
        ]
    
    def set_global_proxy(self, proxy_url: str, force: bool = True) -> None:
        """
        Set global proxy including all possible environment variables

        Args:
            proxy_url: Proxy URL, e.g., 'socks5://user:pass@host:port'
            force: Whether to forcefully override existing proxy settings
        """
        print(f"Setting global proxy: {proxy_url}")

        # Backup original environment variables
        for var in self.proxy_vars:
            if var in os.environ:
                self.original_env[var] = os.environ[var]

        # Set all possible proxy environment variables (both uppercase and lowercase)
        proxy_vars_to_set = {
            'HTTP_PROXY': proxy_url,
            'HTTPS_PROXY': proxy_url,
            'http_proxy': proxy_url,
            'https_proxy': proxy_url,
            'ALL_PROXY': proxy_url,
            'all_proxy': proxy_url,
        }

        for var, value in proxy_vars_to_set.items():
            if force or var not in os.environ:
                os.environ[var] = value
                print(f"  Set {var}={value}")

        # Verify proxy settings
        self.verify_proxy_settings()

    def set_global_proxy_with_conversion(self, proxy_url: str, force: bool = True) -> None:
        """
        設置全局代理，如果是 SOCKS5 則自動轉換為 HTTP

        Args:
            proxy_url: Proxy URL, e.g., 'socks5://user:pass@host:port'
            force: Whether to forcefully override existing proxy settings
        """
        print(f"Setting global proxy with conversion: {proxy_url}")

        # Backup original environment variables
        for var in self.proxy_vars:
            if var in os.environ:
                self.original_env[var] = os.environ[var]

        # 檢查是否為 SOCKS5 代理
        if proxy_url.startswith('socks5://'):
            print("Detected SOCKS5 proxy, converting to HTTP...")
            http_proxy_url = self._start_socks5_to_http_converter(proxy_url)
            if http_proxy_url:
                proxy_url = http_proxy_url
            else:
                print("⚠️ Failed to start SOCKS5 to HTTP converter, using SOCKS5 directly")

        # 設置所有可能的代理環境變量
        proxy_vars_to_set = {
            'HTTP_PROXY': proxy_url,
            'HTTPS_PROXY': proxy_url,
            'http_proxy': proxy_url,
            'https_proxy': proxy_url,
            'ALL_PROXY': proxy_url,
            'all_proxy': proxy_url,
        }

        for var, value in proxy_vars_to_set.items():
            if force or var not in os.environ:
                os.environ[var] = value
                print(f"  Set {var}={value}")

        # 驗證代理設置
        self.verify_proxy_settings()

    def _start_socks5_to_http_converter(self, socks5_url: str) -> Optional[str]:
        """
        啟動 SOCKS5 到 HTTP 的轉換器
        返回 HTTP 代理 URL
        """
        try:
            # 首先檢查 pproxy 是否已安裝
            try:
                import pproxy
            except ImportError:
                print("❌ pproxy not found. Please install it using:")
                print("  uv add pproxy")
                print("  or")
                print("  pip install pproxy")
                return None

            # 如果已有轉換器在運行，先停止它
            self._stop_proxy_converter()

            # 啟動 pproxy 作為後台進程
            # pproxy 使用不同的 URL 格式，需要轉換
            # 從 socks5://user:pass@host:port 轉換為 socks5://host:port#user:pass
            if "@" in socks5_url:
                # 解析 socks5://user:pass@host:port
                scheme_part, rest = socks5_url.split("://", 1)
                if "@" in rest:
                    auth_part, host_part = rest.rsplit("@", 1)
                    pproxy_url = f"{scheme_part}://{host_part}#{auth_part}"
                else:
                    pproxy_url = socks5_url
            else:
                pproxy_url = socks5_url

            cmd = [
                sys.executable, "-m", "pproxy",
                "-l", f"http://0.0.0.0:{self.http_proxy_port}",
                "-r", pproxy_url,
                "-v"  # 詳細輸出用於調試
            ]

            print(f"Starting pproxy converter on port {self.http_proxy_port}...")
            self.proxy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

            # 等待更長時間確保服務啟動 (增加到5秒)
            time.sleep(5)

            # 檢查進程是否成功啟動
            if self.proxy_process.poll() is None:
                http_proxy_url = f"http://127.0.0.1:{self.http_proxy_port}"
                print(f"✅ SOCKS5 to HTTP converter started: {http_proxy_url}")
                
                # 測試轉換後的HTTP代理連接
                test_success = self.test_proxy_connection()
                if not test_success:
                    print("⚠️ HTTP proxy test failed, but continuing...")
                
                return http_proxy_url
            else:
                # 進程已經退出，讀取錯誤信息
                try:
                    _, stderr = self.proxy_process.communicate(timeout=3)
                    stderr_text = stderr.decode() if stderr else "No error output"
                except subprocess.TimeoutExpired:
                    stderr_text = "Process terminated unexpectedly"
                print(f"❌ Failed to start converter: {stderr_text}")
                return None

        except Exception as e:
            print(f"❌ Error starting SOCKS5 to HTTP converter: {e}")
            return None

    def _stop_proxy_converter(self):
        """停止代理轉換器"""
        if self.proxy_process and self.proxy_process.poll() is None:
            print("Stopping proxy converter...")
            self.proxy_process.terminate()
            try:
                self.proxy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.proxy_process.kill()
            self.proxy_process = None

    def clear_proxy(self) -> None:
        """Clear all proxy settings including system-level settings"""
        print("Clearing proxy settings")

        # 停止代理轉換器
        self._stop_proxy_converter()

        # Clear standard proxy environment variables
        for var in self.proxy_vars:
            if var in os.environ:
                del os.environ[var]
                print(f"  Cleared {var}")

        # Force refresh urllib's proxy detection
        self._force_urllib_proxy_refresh()

        # Attempt to clear system-level proxy settings (macOS specific)
        self._clear_system_proxy_macos()
    
    def _force_urllib_proxy_refresh(self):
        """Force refresh urllib's proxy settings"""
        try:
            # Reset urllib's internal proxy cache
            if hasattr(urllib.request, '_opener'):
                urllib.request._opener = None  # type: ignore
            
            # Clear possible proxy detection cache
            if hasattr(urllib.request, 'getproxies_environment'):
                # Force re-detection of environment variables
                pass
        except Exception as e:
            print(f"  urllib proxy refresh warning: {e}")
    
    def _clear_system_proxy_macos(self):
        """Attempt to clear macOS system-level proxy settings"""
        try:
            import subprocess
            
            # Check if on macOS
            if sys.platform == 'darwin':
                # Get current network services
                result = subprocess.run(['networksetup', '-listallnetworkservices'], 
                                      capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    services = result.stdout.strip().split('\n')[1:]  # Skip header line
                    
                    for service in services:
                        if service and not service.startswith('*'):
                            try:
                                # Try to clear HTTP proxy
                                subprocess.run(['networksetup', '-setwebproxystate', service, 'off'], 
                                             capture_output=True, timeout=5)
                                # Try to clear HTTPS proxy
                                subprocess.run(['networksetup', '-setsecurewebproxystate', service, 'off'], 
                                             capture_output=True, timeout=5)
                                # Try to clear SOCKS proxy
                                subprocess.run(['networksetup', '-setsocksfirewallproxystate', service, 'off'], 
                                             capture_output=True, timeout=5)
                            except:
                                pass  # Ignore permission or other errors
                    
                    print("  Attempted to clear macOS system-level proxy settings")
        except Exception as e:
            print(f"  System-level proxy clearing warning: {e}")
    
    def restore_original_proxy(self) -> None:
        """Restore original proxy settings"""
        print("Restoring original proxy settings")
        
        # First clear all current settings
        self.clear_proxy()
        
        # Restore original settings
        for var, value in self.original_env.items():
            os.environ[var] = value
            print(f"  Restored {var}={value}")
    
    def verify_proxy_settings(self) -> Dict[str, str]:
        """Verify current proxy settings"""
        current_proxies = {}
        
        for var in self.proxy_vars:
            if var in os.environ:
                current_proxies[var] = os.environ[var]
        
        print("Current proxy settings:")
        if current_proxies:
            for var, value in current_proxies.items():
                print(f"  {var}={value}")
        else:
            print("  No proxy environment variables")
        
        # Use urllib to verify proxy configuration
        try:
            urllib_proxies = urllib.request.getproxies()
            print("urllib detected proxies:")
            if urllib_proxies:
                for protocol, proxy in urllib_proxies.items():
                    print(f"  {protocol}={proxy}")
            else:
                print("  No proxies detected")
        except Exception as e:
            print(f"  urllib proxy detection failed: {e}")
        
        return current_proxies
    
    def get_all_proxy_sources(self) -> Dict[str, Any]:
        """Get information from all possible proxy sources"""
        sources = {
            'environment_variables': {},
            'urllib_detected': {},
            'system_info': {}
        }
        
        # Environment variables
        for var in self.proxy_vars:
            if var in os.environ:
                sources['environment_variables'][var] = os.environ[var]
        
        # urllib detection
        try:
            sources['urllib_detected'] = urllib.request.getproxies()
        except Exception as e:
            sources['urllib_detected'] = {'error': str(e)}
        
        # System information
        sources['system_info'] = {
            'platform': sys.platform,
            'has_networksetup': sys.platform == 'darwin' and self._check_networksetup_available()
        }
        
        return sources
    
    def _check_networksetup_available(self) -> bool:
        """Check if networksetup command is available"""
        try:
            import subprocess
            result = subprocess.run(['which', 'networksetup'], 
                                  capture_output=True, timeout=2)
            return result.returncode == 0
        except:
            return False
    
    def test_proxy_connection(self, test_url: str = "https://httpbin.org/ip", timeout: int = 30) -> bool:
        """
        Test if proxy connection is working properly
        
        Args:
            test_url: URL for testing
            timeout: Timeout in seconds (increased to 30)
        
        Returns:
            bool: Whether connection is successful
        """
        try:
            import urllib.request
            
            # Create request with proxy
            proxy_handler = urllib.request.ProxyHandler()
            opener = urllib.request.build_opener(proxy_handler)
            urllib.request.install_opener(opener)
            
            with urllib.request.urlopen(test_url, timeout=timeout) as response:
                result = response.read().decode('utf-8')
                print(f"Proxy test successful, response: {result[:100]}...")
                return True
                
        except Exception as e:
            print(f"Proxy test failed: {e}")
            return False
    
    @contextmanager
    def proxy_context(self, proxy_url: Optional[str] = None):
        """
        Proxy context manager to ensure correct proxy settings when entering and exiting
        
        Args:
            proxy_url: Proxy URL, if None then clear proxy
        """
        # Backup current environment
        original_env = dict(os.environ)
        
        try:
            if proxy_url:
                self.set_global_proxy(proxy_url)
                print(f"Entered proxy context: {proxy_url}")
            else:
                self.clear_proxy()
                print("Entered no-proxy context")
            
            yield
            
        finally:
            # Restore original environment
            os.environ.clear()
            os.environ.update(original_env)
            print("Proxy context exited, environment restored")

    def __del__(self):
        """確保在對象銷毀時停止代理轉換器"""
        self._stop_proxy_converter()

def setup_early_proxy(proxy_url: str) -> None:
    """
    Set up proxy at the very beginning of program to ensure all subsequent network requests use proxy
    This function should be called before any other imports and initialization
    """
    print(f"=== Early Proxy Setup ===")
    print(f"Setting proxy: {proxy_url}")
    
    # Set all possible proxy environment variables
    proxy_vars = {
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url,
        'http_proxy': proxy_url,
        'https_proxy': proxy_url,
        'ALL_PROXY': proxy_url,
        'all_proxy': proxy_url,
    }
    
    for var, value in proxy_vars.items():
        os.environ[var] = value
        print(f"Set {var}={value}")
    
    # Force refresh urllib's proxy settings
    if hasattr(urllib.request, '_opener'):
        urllib.request._opener = None  # type: ignore
    
    print(f"=== Early Proxy Setup Complete ===")

def ensure_proxy_persistence():
    """
    Ensure proxy settings remain effective, periodically check and reset
    """
    def check_and_reset_proxy():
        nordvpn_proxy = os.getenv('NORDVPN_PROXY')
        if nordvpn_proxy:
            expected_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
            missing_vars = []
            
            for var in expected_vars:
                if os.environ.get(var) != nordvpn_proxy:
                    missing_vars.append(var)
            
            if missing_vars:
                print(f"Detected missing proxy settings, resetting: {missing_vars}")
                for var in missing_vars:
                    os.environ[var] = nordvpn_proxy
                return True
        return False
    
    return check_and_reset_proxy


# Enhanced proxy management functions for unified architecture
def setup_proxy_for_service(service_type: str, proxy_type: str = ProxyType.AUTO) -> ProxyManager:
    """
    為特定服務設置代理

    Args:
        service_type: 服務類型 ('gemini', 'openrouter', 'azure')
        proxy_type: 代理類型 ('http', 'socks5', 'auto', 'none')

    Returns:
        配置好的 ProxyManager 實例
    """
    proxy_manager = ProxyManager()

    if service_type.lower() == 'gemini':
        # Gemini 需要代理
        nordvpn_proxy = os.getenv('NORDVPN_PROXY')
        if nordvpn_proxy:
            if proxy_type == ProxyType.HTTP or proxy_type == ProxyType.AUTO:
                print(f"🔧 Setting up HTTP proxy for Gemini...")
                proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=True)
            elif proxy_type == ProxyType.SOCKS5:
                print(f"🔧 Setting up SOCKS5 proxy for Gemini...")
                proxy_manager.set_global_proxy(nordvpn_proxy, force=True)
            else:
                print("⚠️ Gemini requires proxy, but proxy_type is 'none'")
        else:
            print("⚠️ No proxy configured for Gemini")

    elif service_type.lower() in ['openrouter', 'azure']:
        # OpenRouter 和 Azure 不需要代理
        print(f"🔧 Clearing proxy for {service_type}...")
        proxy_manager.clear_proxy()

    else:
        print(f"⚠️ Unknown service type: {service_type}")

    return proxy_manager


def get_proxy_info() -> Dict[str, Any]:
    """
    獲取當前代理信息

    Returns:
        包含代理信息的字典
    """
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY', 'all_proxy']
    proxy_info = {}

    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            proxy_info[var] = value

    # 檢測代理類型
    proxy_type = ProxyType.NONE
    if proxy_info:
        first_proxy = list(proxy_info.values())[0]
        if first_proxy.startswith('http://'):
            proxy_type = ProxyType.HTTP
        elif first_proxy.startswith('socks5://'):
            proxy_type = ProxyType.SOCKS5

    return {
        'proxy_vars': proxy_info,
        'proxy_type': proxy_type,
        'has_proxy': bool(proxy_info)
    }


def validate_proxy_for_service(service_type: str) -> bool:
    """
    驗證服務的代理配置是否正確

    Args:
        service_type: 服務類型

    Returns:
        True 如果代理配置正確
    """
    proxy_info = get_proxy_info()

    if service_type.lower() == 'gemini':
        # Gemini 需要代理
        if not proxy_info['has_proxy']:
            print(f"❌ Gemini requires proxy but none is configured")
            return False
        print(f"✅ Proxy configured for Gemini: {proxy_info['proxy_type']}")
        return True

    elif service_type.lower() in ['openrouter', 'azure']:
        # OpenRouter 和 Azure 不需要代理
        if proxy_info['has_proxy']:
            print(f"⚠️ {service_type} doesn't need proxy but one is configured")
        else:
            print(f"✅ No proxy configured for {service_type} (correct)")
        return True

    else:
        print(f"⚠️ Unknown service type: {service_type}")
        return False
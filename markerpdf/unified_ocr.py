#!/usr/bin/env python3
"""
Unified OCR interface module

This module provides a single interface that replaces the duplicate HTTP/SOCKS5 proxy modules
while preserving all functionality. It serves as the main entry point for OCR operations.
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# Early proxy setup before any other imports
from dotenv import load_dotenv
load_dotenv()

from .services import (
    OCRServiceFactory, 
    ProxyType,
    create_gemini_ocr,
    create_openrouter_ocr,
    create_default_ocr,
    test_gemini_http,
    test_gemini_socks5,
    test_openrouter
)
from .core import save_results_to_markdown
from .proxy_manager import setup_proxy_for_service, validate_proxy_for_service, get_proxy_info


# 統一的配置創建函數
def create_gemini_config(proxy_type: str = "http", proxy_timeout: int = 30) -> Dict[str, Any]:
    """
    創建 Gemini 模型配置
    
    Args:
        proxy_type: 代理類型 ("http", "socks5", "auto")
        proxy_timeout: 代理超時時間（秒）
        
    Returns:
        配置字典
    """
    print(f"\n{'='*50}")
    print("Configuring Gemini model (requires proxy)")
    print(f"{'='*50}")
    
    # 設置代理
    proxy_manager = setup_proxy_for_service('gemini', proxy_type)
    
    # 測試代理連接
    print(f"🧪 Testing proxy connection (timeout: {proxy_timeout}s)...")
    if not proxy_manager.test_proxy_connection(timeout=proxy_timeout):
        print("⚠️ Proxy connection test failed, but continuing execution")
    else:
        print("✅ Proxy connection test successful")
    
    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_GEMINI_SERIVES'),
        "gemini_api_key": os.getenv('GEMINI_API_KEY'),
        "gemini_model_name": os.getenv('GEMINI_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
        "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
        "langs": os.getenv('MARKER_LANG', 'en').split(','),
        "proxy_timeout": proxy_timeout,
        "proxy_type": proxy_type
    }
    return config


def create_gemini_config_with_http_proxy(proxy_timeout: int = 30) -> Dict[str, Any]:
    """
    創建使用 HTTP 代理的 Gemini 配置（向後兼容）
    
    Args:
        proxy_timeout: 代理超時時間（秒）
        
    Returns:
        配置字典
    """
    return create_gemini_config(proxy_type="http", proxy_timeout=proxy_timeout)


def create_openrouter_config() -> Dict[str, Any]:
    """
    創建 OpenRouter 模型配置
    
    Returns:
        配置字典
    """
    print(f"\n{'='*50}")
    print("Configuring OpenRouter model (clearing proxy)")
    print(f"{'='*50}")
    
    # 清除代理設置
    proxy_manager = setup_proxy_for_service('openrouter', 'none')
    
    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_OPENROUTER_SERIVES'),
        "openai_api_key": os.getenv('OPENROUTER_API_KEY'),
        "openai_base_url": os.getenv('OPENROUTER_URL'),
        "openai_model": os.getenv('OPENROUTER_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
        "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
        "langs": os.getenv('MARKER_LANG', 'en').split(',')
    }
    return config


# 統一的測試函數
def test_model(config: Dict[str, Any], model_name: str, pdf_path: str = "sample.pdf") -> Dict[str, Any]:
    """
    測試指定模型的 OCR 性能（統一接口）
    
    Args:
        config: 模型配置
        model_name: 模型名稱
        pdf_path: PDF 文件路徑
        
    Returns:
        測試結果字典
    """
    return test_model_with_retry(config, model_name, pdf_path)


def test_model_with_retry(config: Dict[str, Any], model_name: str, 
                         pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
    """
    使用重試機制測試模型（統一接口）
    
    Args:
        config: 模型配置
        model_name: 模型名稱
        pdf_path: PDF 文件路徑
        max_retries: 最大重試次數
        
    Returns:
        測試結果字典
    """
    # 根據配置確定服務類型
    if "gemini" in config.get("llm_service", "").lower():
        proxy_type_str = config.get("proxy_type", "http")
        if proxy_type_str == "http":
            proxy_type = ProxyType.HTTP
        elif proxy_type_str == "socks5":
            proxy_type = ProxyType.SOCKS5
        else:
            proxy_type = ProxyType.AUTO
        
        ocr = create_gemini_ocr(proxy_type=proxy_type, proxy_timeout=config.get("proxy_timeout", 30))
    elif "openrouter" in config.get("llm_service", "").lower():
        ocr = create_openrouter_ocr()
    else:
        # 默認使用 Gemini HTTP 代理
        ocr = create_default_ocr()
    
    return ocr.test_model_with_retry(pdf_path, max_retries)


# HTTP 代理特定函數（向後兼容）
def test_model_http_proxy(config: Dict[str, Any], model_name: str, pdf_path: str = "sample.pdf") -> Dict[str, Any]:
    """測試 HTTP 代理模型（向後兼容）"""
    config["proxy_type"] = "http"
    return test_model_with_retry(config, model_name, pdf_path)


def test_model_with_http_proxy_retry(config: Dict[str, Any], model_name: str, 
                                   pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
    """使用 HTTP 代理重試測試模型（向後兼容）"""
    config["proxy_type"] = "http"
    return test_model_with_retry(config, model_name, pdf_path, max_retries)


# 輸出保存函數
def save_model_output(text: str, images: Dict, model_name: str, 
                     config: Dict[str, Any], timestamp: str) -> str:
    """
    保存模型輸出結果
    
    Args:
        text: 提取的文本
        images: 提取的圖片
        model_name: 模型名稱
        config: 配置信息
        timestamp: 時間戳
        
    Returns:
        輸出文件夾路徑
    """
    # 創建輸出文件夾
    safe_model_name = model_name.replace(" ", "_").replace("(", "").replace(")", "")
    output_folder = f"output_{safe_model_name}_{timestamp}"
    Path(output_folder).mkdir(exist_ok=True)
    
    # 保存提取的文本
    text_file = Path(output_folder) / "extracted_text.md"
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(f"# OCR Results - {model_name}\n\n")
        f.write(f"**Processing Time:** {timestamp}\n\n")
        f.write("**Configuration:**\n")
        for key, value in config.items():
            if key not in ['gemini_api_key', 'openai_api_key']:  # 不記錄敏感信息
                f.write(f"- {key}: {value}\n")
        f.write("\n**Extracted Text:**\n\n")
        f.write(text)
    
    # 保存圖片
    if images:
        images_folder = Path(output_folder) / "images"
        images_folder.mkdir(exist_ok=True)
        
        for i, (page_num, image_data) in enumerate(images.items()):
            if hasattr(image_data, 'save'):
                image_path = images_folder / f"page_{page_num}_image_{i}.png"
                image_data.save(image_path)
    
    print(f"📁 Results saved to: {output_folder}")
    return output_folder


# 便利函數
def get_available_services() -> List[str]:
    """獲取可用的 OCR 服務列表"""
    return OCRServiceFactory.get_available_services()


def create_service(service_name: str, **kwargs):
    """創建 OCR 服務實例"""
    return OCRServiceFactory.create_service(service_name, **kwargs)


def get_current_proxy_info() -> Dict[str, Any]:
    """獲取當前代理信息"""
    return get_proxy_info()


def validate_service_proxy(service_type: str) -> bool:
    """驗證服務的代理配置"""
    return validate_proxy_for_service(service_type)


# 主要測試函數
def main():
    """主測試函數"""
    print("=== Unified OCR Interface Test ===")
    print("Testing default configuration (HTTP proxy + Gemini)")
    print("="*50)
    
    # 測試默認配置（HTTP 代理 + Gemini）
    gemini_config = create_gemini_config_with_http_proxy()
    gemini_result = test_model_http_proxy(gemini_config, "Gemini 2.5 Flash (HTTP Proxy)")
    
    if gemini_result['success']:
        print(f"\n✅ Gemini HTTP proxy test successful!")
        print(f"Processing time: {gemini_result['processing_time']:.2f}s")
        print(f"Output folder: {gemini_result['output_folder']}")
    else:
        print(f"\n❌ Gemini HTTP proxy test failed: {gemini_result['error']}")
    
    # 保存結果
    save_results_to_markdown([gemini_result], "unified_ocr_test_results.md")


if __name__ == "__main__":
    main()

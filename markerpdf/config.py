"""
Configuration management for marker-pdf OCR processing
Centralized configuration creation for different models and services
"""

import os
from typing import Dict, Any, List
from abc import ABC, abstractmethod


class BaseConfig(ABC):
    """Base configuration class for OCR models"""
    
    def __init__(self):
        """Initialize base configuration with common settings"""
        self.config = {
            "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
            "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
            "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
            "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
            "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
            "langs": os.getenv('MARKER_LANG', 'en').split(',')
        }
    
    @abstractmethod
    def get_config(self) -> Dict[str, Any]:
        """Get the complete configuration dictionary"""
        pass
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """Update configuration with new values"""
        self.config.update(updates)


class GeminiConfig(BaseConfig):
    """Configuration for Google Gemini models"""
    
    def __init__(self, proxy_timeout: int = 30):
        """
        Initialize Gemini configuration
        
        Args:
            proxy_timeout: Timeout for proxy connections in seconds
        """
        super().__init__()
        
        # Gemini-specific configuration
        gemini_config = {
            "llm_service": os.getenv('MARKER_GEMINI_SERIVES'),
            "gemini_api_key": os.getenv('GEMINI_API_KEY'),
            "gemini_model_name": os.getenv('GEMINI_MODEL'),
            "proxy_timeout": proxy_timeout
        }
        
        self.config.update(gemini_config)
        
        # Validate required Gemini settings
        self._validate_gemini_config()
    
    def _validate_gemini_config(self) -> None:
        """Validate that required Gemini configuration is present"""
        required_keys = ['gemini_api_key', 'gemini_model_name']
        missing_keys = [key for key in required_keys if not self.config.get(key)]
        
        if missing_keys:
            print(f"⚠️ Missing Gemini configuration: {', '.join(missing_keys)}")
            print("Please check your environment variables:")
            for key in missing_keys:
                env_var = key.upper()
                print(f"  - {env_var}")
    
    def get_config(self) -> Dict[str, Any]:
        """Get complete Gemini configuration"""
        return self.config.copy()


class OpenRouterConfig(BaseConfig):
    """Configuration for OpenRouter models"""
    
    def __init__(self):
        """Initialize OpenRouter configuration"""
        super().__init__()
        
        # OpenRouter-specific configuration
        openrouter_config = {
            "llm_service": os.getenv('MARKER_OPENROUTER_SERVICES'),
            "openrouter_api_key": os.getenv('OPENROUTER_API_KEY'),
            "openrouter_model_name": os.getenv('OPENROUTER_MODEL'),
            "openrouter_base_url": os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')
        }
        
        self.config.update(openrouter_config)
        
        # Validate required OpenRouter settings
        self._validate_openrouter_config()
    
    def _validate_openrouter_config(self) -> None:
        """Validate that required OpenRouter configuration is present"""
        required_keys = ['openrouter_api_key', 'openrouter_model_name']
        missing_keys = [key for key in required_keys if not self.config.get(key)]
        
        if missing_keys:
            print(f"⚠️ Missing OpenRouter configuration: {', '.join(missing_keys)}")
            print("Please check your environment variables:")
            for key in missing_keys:
                env_var = key.upper()
                print(f"  - {env_var}")
    
    def get_config(self) -> Dict[str, Any]:
        """Get complete OpenRouter configuration"""
        return self.config.copy()


class AzureConfig(BaseConfig):
    """Configuration for Azure Document AI"""
    
    def __init__(self):
        """Initialize Azure configuration"""
        super().__init__()
        
        # Azure-specific configuration
        azure_config = {
            "azure_endpoint": os.getenv('AZURE_DOCUMENT_AI_ENDPOINT'),
            "azure_api_key": os.getenv('AZURE_DOCUMENT_AI_KEY'),
            "azure_api_version": os.getenv('AZURE_DOCUMENT_AI_VERSION', '2023-07-31')
        }
        
        self.config.update(azure_config)
        
        # Validate required Azure settings
        self._validate_azure_config()
    
    def _validate_azure_config(self) -> None:
        """Validate that required Azure configuration is present"""
        required_keys = ['azure_endpoint', 'azure_api_key']
        missing_keys = [key for key in required_keys if not self.config.get(key)]
        
        if missing_keys:
            print(f"⚠️ Missing Azure configuration: {', '.join(missing_keys)}")
            print("Please check your environment variables:")
            for key in missing_keys:
                env_var = key.upper()
                print(f"  - {env_var}")
    
    def get_config(self) -> Dict[str, Any]:
        """Get complete Azure configuration"""
        return self.config.copy()


# Factory functions for creating configurations
def create_gemini_config(proxy_timeout: int = 30) -> Dict[str, Any]:
    """
    Create Gemini model configuration
    
    Args:
        proxy_timeout: Timeout for proxy connections in seconds
        
    Returns:
        Dictionary containing Gemini configuration
    """
    print(f"\n{'='*50}")
    print("Configuring Gemini model")
    print(f"{'='*50}")
    
    config = GeminiConfig(proxy_timeout=proxy_timeout)
    
    # Display configuration summary
    gemini_config = config.get_config()
    print(f"📋 Configuration Summary:")
    print(f"  - Model: {gemini_config.get('gemini_model_name', 'Not set')}")
    print(f"  - Service: {gemini_config.get('llm_service', 'Not set')}")
    print(f"  - Use LLM: {gemini_config.get('use_llm', False)}")
    print(f"  - Output Format: {gemini_config.get('output_format', 'markdown')}")
    print(f"  - Proxy Timeout: {gemini_config.get('proxy_timeout', 30)}s")
    
    return gemini_config


def create_openrouter_config() -> Dict[str, Any]:
    """
    Create OpenRouter model configuration
    
    Returns:
        Dictionary containing OpenRouter configuration
    """
    print(f"\n{'='*50}")
    print("Configuring OpenRouter model")
    print(f"{'='*50}")
    
    config = OpenRouterConfig()
    
    # Display configuration summary
    openrouter_config = config.get_config()
    print(f"📋 Configuration Summary:")
    print(f"  - Model: {openrouter_config.get('openrouter_model_name', 'Not set')}")
    print(f"  - Service: {openrouter_config.get('llm_service', 'Not set')}")
    print(f"  - Base URL: {openrouter_config.get('openrouter_base_url', 'Not set')}")
    print(f"  - Use LLM: {openrouter_config.get('use_llm', False)}")
    print(f"  - Output Format: {openrouter_config.get('output_format', 'markdown')}")
    
    return openrouter_config


def create_azure_config() -> Dict[str, Any]:
    """
    Create Azure Document AI configuration
    
    Returns:
        Dictionary containing Azure configuration
    """
    print(f"\n{'='*50}")
    print("Configuring Azure Document AI")
    print(f"{'='*50}")
    
    config = AzureConfig()
    
    # Display configuration summary
    azure_config = config.get_config()
    print(f"📋 Configuration Summary:")
    print(f"  - Endpoint: {azure_config.get('azure_endpoint', 'Not set')}")
    print(f"  - API Version: {azure_config.get('azure_api_version', 'Not set')}")
    print(f"  - Output Format: {azure_config.get('output_format', 'markdown')}")
    
    return azure_config


def create_custom_config(base_config: Dict[str, Any], 
                        model_type: str = "custom") -> Dict[str, Any]:
    """
    Create a custom configuration by merging with base settings
    
    Args:
        base_config: Custom configuration dictionary
        model_type: Type of model for logging purposes
        
    Returns:
        Dictionary containing merged configuration
    """
    print(f"\n{'='*50}")
    print(f"Configuring {model_type} model")
    print(f"{'='*50}")
    
    # Start with base configuration
    config = BaseConfig()
    base_settings = config.config.copy()
    
    # Merge with custom settings
    base_settings.update(base_config)
    
    print(f"📋 Custom Configuration Applied:")
    for key, value in base_config.items():
        print(f"  - {key}: {value}")
    
    return base_settings

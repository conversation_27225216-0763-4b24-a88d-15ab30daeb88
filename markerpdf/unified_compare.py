#!/usr/bin/env python3
"""
Unified proxy comparison using the new architecture

This module provides simplified proxy comparison functionality using the unified OCR architecture.
It replaces the complex comparison logic with a cleaner implementation.
"""

import os
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from dotenv import load_dotenv
load_dotenv()

from .services import create_gemini_ocr, ProxyType
from .core import save_results_to_markdown


def test_socks5_proxy_with_timing(config: Dict[str, Any], model_name: str, 
                                 pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
    """
    測試 SOCKS5 代理性能（使用統一架構）
    
    Args:
        config: 配置字典
        model_name: 模型名稱
        pdf_path: PDF 文件路徑
        max_retries: 最大重試次數
        
    Returns:
        測試結果字典
    """
    print(f"\n{'='*50}")
    print(f"Testing SOCKS5 Proxy for {model_name}")
    print(f"{'='*50}")
    
    # 記錄設置開始時間
    setup_start_time = time.time()
    
    # 使用統一 OCR 服務與 SOCKS5 代理
    ocr = create_gemini_ocr(proxy_type=ProxyType.SOCKS5, proxy_timeout=config.get('proxy_timeout', 30))
    
    setup_time = time.time() - setup_start_time
    print(f"⏱️ SOCKS5 proxy setup time: {setup_time:.2f} seconds")
    
    # 記錄處理開始時間
    processing_start_time = time.time()
    
    # 執行 OCR 測試
    result = ocr.test_model_with_retry(pdf_path, max_retries)
    
    processing_time = time.time() - processing_start_time
    
    if result['success']:
        # 更新結果以包含時間信息
        result.update({
            'setup_time': setup_time,
            'total_time': processing_time + setup_time,
            'proxy_type': 'SOCKS5'
        })
        print(f"✅ SOCKS5 proxy test completed successfully!")
        print(f"⏱️ Setup time: {setup_time:.2f}s, Processing time: {processing_time:.2f}s")
    else:
        print(f"❌ SOCKS5 proxy test failed: {result['error']}")
        result.update({
            'setup_time': setup_time,
            'total_time': processing_time + setup_time,
            'proxy_type': 'SOCKS5'
        })
    
    return result


def test_http_proxy_with_timing(config: Dict[str, Any], model_name: str, 
                               pdf_path: str = "sample.pdf", max_retries: int = 3) -> Dict[str, Any]:
    """
    測試 HTTP 代理性能（使用統一架構）
    
    Args:
        config: 配置字典
        model_name: 模型名稱
        pdf_path: PDF 文件路徑
        max_retries: 最大重試次數
        
    Returns:
        測試結果字典
    """
    print(f"\n{'='*50}")
    print(f"Testing HTTP Proxy (SOCKS5->HTTP conversion) for {model_name}")
    print(f"{'='*50}")
    
    # 記錄設置開始時間
    setup_start_time = time.time()
    
    # 使用統一 OCR 服務與 HTTP 代理
    ocr = create_gemini_ocr(proxy_type=ProxyType.HTTP, proxy_timeout=config.get('proxy_timeout', 30))
    
    setup_time = time.time() - setup_start_time
    print(f"⏱️ HTTP proxy setup + conversion time: {setup_time:.2f} seconds")
    
    # 記錄處理開始時間
    processing_start_time = time.time()
    
    # 執行 OCR 測試
    result = ocr.test_model_with_retry(pdf_path, max_retries)
    
    processing_time = time.time() - processing_start_time
    
    if result['success']:
        # 更新結果以包含時間信息
        result.update({
            'setup_time': setup_time,
            'total_time': processing_time + setup_time,
            'proxy_type': 'HTTP (converted from SOCKS5)'
        })
        print(f"✅ HTTP proxy test completed successfully!")
        print(f"⏱️ Setup time: {setup_time:.2f}s, Processing time: {processing_time:.2f}s")
    else:
        print(f"❌ HTTP proxy test failed: {result['error']}")
        result.update({
            'setup_time': setup_time,
            'total_time': processing_time + setup_time,
            'proxy_type': 'HTTP (converted from SOCKS5)'
        })
    
    return result


def save_comparison_results(socks5_result: Dict[str, Any], http_result: Dict[str, Any], 
                          output_file: str = "proxy_comparison_results.md") -> str:
    """
    保存代理比較結果
    
    Args:
        socks5_result: SOCKS5 代理測試結果
        http_result: HTTP 代理測試結果
        output_file: 輸出文件名
        
    Returns:
        輸出文件路徑
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Proxy Performance Comparison Results\n\n")
        f.write(f"**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # SOCKS5 結果
        f.write("## SOCKS5 Proxy Results\n\n")
        f.write(f"- **Model:** {socks5_result['model_name']}\n")
        f.write(f"- **Success:** {'✅' if socks5_result['success'] else '❌'}\n")
        f.write(f"- **Setup Time:** {socks5_result.get('setup_time', 0):.2f} seconds\n")
        f.write(f"- **Processing Time:** {socks5_result['processing_time']:.2f} seconds\n")
        f.write(f"- **Total Time:** {socks5_result.get('total_time', 0):.2f} seconds\n")
        f.write(f"- **Text Length:** {socks5_result['text_length']} characters\n")
        f.write(f"- **Images:** {socks5_result['image_count']}\n")
        f.write(f"- **Attempts:** {socks5_result['attempts']}\n")
        
        if not socks5_result['success']:
            f.write(f"- **Error:** {socks5_result['error']}\n")
        
        if socks5_result['output_folder']:
            f.write(f"- **Output Folder:** {socks5_result['output_folder']}\n")
        
        f.write("\n")
        
        # HTTP 結果
        f.write("## HTTP Proxy Results\n\n")
        f.write(f"- **Model:** {http_result['model_name']}\n")
        f.write(f"- **Success:** {'✅' if http_result['success'] else '❌'}\n")
        f.write(f"- **Setup Time:** {http_result.get('setup_time', 0):.2f} seconds\n")
        f.write(f"- **Processing Time:** {http_result['processing_time']:.2f} seconds\n")
        f.write(f"- **Total Time:** {http_result.get('total_time', 0):.2f} seconds\n")
        f.write(f"- **Text Length:** {http_result['text_length']} characters\n")
        f.write(f"- **Images:** {http_result['image_count']}\n")
        f.write(f"- **Attempts:** {http_result['attempts']}\n")
        
        if not http_result['success']:
            f.write(f"- **Error:** {http_result['error']}\n")
        
        if http_result['output_folder']:
            f.write(f"- **Output Folder:** {http_result['output_folder']}\n")
        
        f.write("\n")
        
        # 比較分析
        f.write("## Performance Comparison\n\n")
        
        if socks5_result['success'] and http_result['success']:
            socks5_total = socks5_result.get('total_time', 0)
            http_total = http_result.get('total_time', 0)
            
            if socks5_total < http_total:
                faster = "SOCKS5"
                time_diff = http_total - socks5_total
            else:
                faster = "HTTP"
                time_diff = socks5_total - http_total
            
            f.write(f"- **Faster Proxy:** {faster} (by {time_diff:.2f} seconds)\n")
            f.write(f"- **SOCKS5 Total Time:** {socks5_total:.2f}s\n")
            f.write(f"- **HTTP Total Time:** {http_total:.2f}s\n")
            
            # 設置時間比較
            socks5_setup = socks5_result.get('setup_time', 0)
            http_setup = http_result.get('setup_time', 0)
            f.write(f"- **SOCKS5 Setup Time:** {socks5_setup:.2f}s\n")
            f.write(f"- **HTTP Setup Time:** {http_setup:.2f}s (includes SOCKS5->HTTP conversion)\n")
        else:
            f.write("- **Note:** Cannot compare performance due to test failures\n")
        
        f.write("\n")
        
        # 建議
        f.write("## Recommendations\n\n")
        if socks5_result['success'] and http_result['success']:
            if http_result.get('total_time', 0) <= socks5_result.get('total_time', 0):
                f.write("- **Recommended:** HTTP proxy (better or equal performance)\n")
                f.write("- **Reason:** HTTP proxy provides good performance and better compatibility\n")
            else:
                f.write("- **Recommended:** SOCKS5 proxy (better performance)\n")
                f.write("- **Reason:** SOCKS5 proxy shows better performance in this test\n")
        elif http_result['success']:
            f.write("- **Recommended:** HTTP proxy (only working option)\n")
        elif socks5_result['success']:
            f.write("- **Recommended:** SOCKS5 proxy (only working option)\n")
        else:
            f.write("- **Issue:** Both proxy types failed, check proxy configuration\n")
    
    print(f"📄 Proxy comparison results saved to: {output_file}")
    return output_file


def main():
    """主要比較函數"""
    print("=== Unified Proxy Performance Comparison ===")
    print("Testing SOCKS5 vs HTTP proxy performance using unified architecture")
    print("="*60)
    
    # 創建 Gemini 配置
    from .unified_ocr import create_gemini_config
    config = create_gemini_config(proxy_type="auto")  # 將在各自測試中覆蓋
    
    # 測試 SOCKS5 代理
    socks5_result = test_socks5_proxy_with_timing(config, "Gemini 2.5 Flash (SOCKS5)")
    
    # 測試 HTTP 代理
    http_result = test_http_proxy_with_timing(config, "Gemini 2.5 Flash (HTTP)")
    
    # 保存比較結果
    report_file = save_comparison_results(socks5_result, http_result)
    
    print(f"\n{'='*60}")
    print("Proxy comparison completed!")
    print(f"Detailed comparison report: {report_file}")
    print(f"{'='*60}")
    
    # 打印摘要
    print("\n📊 Comparison Summary:")
    print(f"  SOCKS5: {'✅ Success' if socks5_result['success'] else '❌ Failed'} "
          f"({socks5_result.get('total_time', 0):.2f}s total)")
    print(f"  HTTP:   {'✅ Success' if http_result['success'] else '❌ Failed'} "
          f"({http_result.get('total_time', 0):.2f}s total)")


if __name__ == "__main__":
    main()

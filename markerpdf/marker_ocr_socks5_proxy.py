import os
import time
from datetime import datetime
from pathlib import Path

# Early proxy setup before any other imports
from dotenv import load_dotenv
load_dotenv()

# Early proxy setup - must be executed before other library imports
nordvpn_proxy = os.getenv('NORDVPN_PROXY')
if nordvpn_proxy:
    from .proxy_manager import setup_early_proxy
    setup_early_proxy(nordvpn_proxy)

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered
from .proxy_manager import ProxyManager, ensure_proxy_persistence

# Create global proxy manager
proxy_manager = ProxyManager()

def create_gemini_config():
    """Create Gemini model configuration"""
    print(f"\n{'='*50}")
    print("Configuring Gemini model (requires proxy)")
    print(f"{'='*50}")
    
    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
    if nordvpn_proxy:
        # Use enhanced proxy setup
        proxy_manager.set_global_proxy(nordvpn_proxy, force=True)
        
        # Test proxy connection
        print("Testing proxy connection...")
        if not proxy_manager.test_proxy_connection():
            print("⚠️ Proxy connection test failed, but continuing execution")
        else:
            print("✅ Proxy connection test successful")
    else:
        print("⚠️ No proxy configured, may not be able to access Gemini API")

    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_GEMINI_SERIVES'),
        "gemini_api_key": os.getenv('GEMINI_API_KEY'),
        "gemini_model_name": os.getenv('GEMINI_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
        "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
        "langs": os.getenv('MARKER_LANG', 'en').split(',')
    }
    return config

def create_openrouter_config():
    """Create OpenRouter Deepseek model configuration"""
    print(f"\n{'='*50}")
    print("Configuring OpenRouter model (clearing proxy)")
    print(f"{'='*50}")
    
    # Clear proxy settings (OpenRouter doesn't need proxy)
    proxy_manager.clear_proxy()

    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_OPENROUTER_SERIVES'),
        "openai_api_key": os.getenv('OPENROUTER_API_KEY'),
        "openai_base_url": os.getenv('OPENROUTER_URL'),
        "openai_model": os.getenv('OPENROUTER_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
        "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
        "langs": os.getenv('MARKER_LANG', 'en').split(',')
    }
    return config

def save_model_output(text, images, model_name, config, timestamp, base_dir="ocr_comparison_results"):
    """Save model output to separate folder, including markdown files and images"""
    # Create model-specific folder
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)

    # Save markdown file
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR Results\n\n")
        f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Add configuration information
        f.write("## Configuration Parameters\n\n")
        f.write(f"- **Batch Multiplier**: {config.get('batch_multiplier', 1)}\n")
        f.write(f"- **Languages**: {', '.join(config.get('langs', ['en']))}\n")
        f.write(f"- **Use LLM**: {config.get('use_llm', True)}\n")
        f.write(f"- **Force OCR**: {config.get('force_ocr', False)}\n")
        f.write(f"- **Format Lines**: {config.get('format_lines', True)}\n\n")

        f.write("## Extracted Text Content\n\n")
        f.write(text)

    # Save image files
    saved_images = []
    for i, (image_name, image_data) in enumerate(images.items()):
        # Use original image name if available, otherwise use index
        if image_name:
            image_filename = f"{image_name}"
        else:
            image_filename = f"image_{i+1}.png"

        image_path = model_folder / image_filename

        # Save image
        try:
            image_data.save(image_path)
            saved_images.append(image_filename)
            print(f"  Saved image: {image_filename}")
        except Exception as e:
            print(f"  Failed to save image {image_filename}: {e}")

    # Create image list file
    if saved_images:
        images_list_file = model_folder / "images_list.md"
        with open(images_list_file, 'w', encoding='utf-8') as f:
            f.write(f"# {model_name} Extracted Images List\n\n")
            f.write(f"Total extracted images: {len(saved_images)}\n\n")
            for img_name in saved_images:
                f.write(f"- ![{img_name}]({img_name})\n")

    print(f"  Model output saved to: {model_folder}")
    return model_folder

def test_model_with_retry(config, model_name, pdf_path="sample.pdf", max_retries=3):
    """Test specified model's OCR performance with retry mechanism"""
    print(f"\n{'='*50}")
    print(f"Starting test for {model_name}")
    print(f"{'='*50}")
    
    # Create proxy check function
    check_proxy = ensure_proxy_persistence()
    error_message = "Unknown error occurred"

    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"\nAttempt {attempt + 1}...")
                # Re-check and set proxy
                if check_proxy():
                    print("Proxy has been reset")
                
                # If Gemini model, ensure proxy is correctly set
                if "Gemini" in model_name:
                    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
                    if nordvpn_proxy:
                        proxy_manager.set_global_proxy(nordvpn_proxy, force=True)
                        print("Forced Gemini proxy reset")
            
            # Create configuration parser and converter
            config_parser = ConfigParser(config)
            converter = PdfConverter(
                config=config_parser.generate_config_dict(),
                artifact_dict=create_model_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer(),
                llm_service=config_parser.get_llm_service()
            )

            # Record start time
            start_time = time.time()
            print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # Execute OCR conversion
            rendered = converter(pdf_path)
            text, _, images = text_from_rendered(rendered)

            # Record end time
            end_time = time.time()
            processing_time = end_time - start_time

            print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Processing time: {processing_time:.2f} seconds")
            print(f"Text length: {len(text)} characters")
            print(f"Image count: {len(images)}")

            # Save model output to separate folder
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_folder = save_model_output(text, images, model_name, config, timestamp)

            return {
                'model_name': model_name,
                'processing_time': processing_time,
                'text_length': len(text),
                'image_count': len(images),
                'text_content': text,
                'images': images,
                'output_folder': output_folder,
                'config': config,
                'success': True,
                'error': None,
                'attempts': attempt + 1
            }

        except Exception as e:
            error_message = str(e)
            print(f"Attempt {attempt + 1} failed: {error_message}")
            
            # Check if it's a geolocation error
            if "FAILED_PRECONDITION" in error_message and "location is not supported" in error_message:
                print("⚠️ Detected geolocation restriction error, this may be a proxy configuration issue")
                
                # If there are remaining retries, wait before retrying
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5  # Incremental wait time
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
            else:
                # Non-proxy related error, fail immediately
                break

    # All retries failed
    return {
        'model_name': model_name,
        'processing_time': 0,
        'text_length': 0,
        'image_count': 0,
        'text_content': '',
        'images': {},
        'output_folder': None,
        'config': config,
        'success': False,
        'error': error_message,
        'attempts': max_retries
    }

def test_model(config, model_name, pdf_path="sample.pdf"):
    """Test specified model's OCR performance (maintain original interface compatibility)"""
    return test_model_with_retry(config, model_name, pdf_path)

def save_results_to_markdown(results, output_dir="ocr_comparison_results"):
    """Save test results to Markdown file"""
    # Create output directory
    Path(output_dir).mkdir(exist_ok=True)

    # Generate timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Create comparison report
    comparison_file = Path(output_dir) / f"ocr_comparison_summary_{timestamp}.md"

    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("# OCR Model Comparison Test Report\n\n")
        f.write(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Performance comparison table
        f.write("## Performance Comparison\n\n")
        f.write("| Model | Processing Time(s) | Text Length(chars) | Image Count | Batch Multiplier | Languages | Retry Count | Output Folder | Status |\n")
        f.write("|-------|-------------------|-------------------|-------------|-----------------|-----------|-------------|---------------|--------|\n")

        for result in results:
            status = "✅ Success" if result['success'] else "❌ Failed"
            folder_name = result['output_folder'].name if result['output_folder'] else "N/A"
            batch_multiplier = result.get('config', {}).get('batch_multiplier', 1)
            langs = ', '.join(result.get('config', {}).get('langs', ['en']))
            attempts = result.get('attempts', 1)
            f.write(f"| {result['model_name']} | {result['processing_time']:.2f} | {result['text_length']} | {result['image_count']} | {batch_multiplier} | {langs} | {attempts} | `{folder_name}` | {status} |\n")

        f.write("\n")

        # Folder structure description
        f.write("## Output Folder Structure\n\n")
        f.write("Each model's results are saved in separate folders containing the following files:\n\n")
        f.write("```\n")
        f.write("ModelName_Timestamp/\n")
        f.write("├── ocr_result.md      # Complete OCR extracted text content\n")
        f.write("├── images_list.md     # List of extracted images\n")
        f.write("├── image_1.png        # Extracted image files\n")
        f.write("├── image_2.png        # ...\n")
        f.write("└── ...\n")
        f.write("```\n\n")

        # Detailed result summary
        for result in results:
            f.write(f"## {result['model_name']} Result Summary\n\n")

            if result['success']:
                f.write(f"- **Processing Time**: {result['processing_time']:.2f} seconds\n")
                f.write(f"- **Text Length**: {result['text_length']} characters\n")
                f.write(f"- **Image Count**: {result['image_count']}\n")
                f.write(f"- **Retry Count**: {result.get('attempts', 1)}\n")
                f.write(f"- **Output Folder**: `{result['output_folder'].name}`\n\n")
                f.write("### Image Segmentation Results\n\n")
                if result['images']:
                    for img_name in result['images'].keys():
                        f.write(f"- {img_name}\n")
                else:
                    f.write("- No images extracted\n")
                f.write(f"\n**Full content see**: `{result['output_folder']}/ocr_result.md`\n\n")
            else:
                f.write(f"- **Error**: {result['error']}\n")
                f.write(f"- **Retry Count**: {result.get('attempts', 1)}\n\n")

    print(f"\nComparison summary report saved to: {comparison_file}")
    return comparison_file

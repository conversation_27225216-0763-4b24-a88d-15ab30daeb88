#!/usr/bin/env python3
"""
Compare performance differences between SOCKS5 and HTTP proxy for Gemini API usage
Test compatibility and behavioral differences between two proxy types
Ensure timing starts only after proxy setup is complete for accurate performance comparison
"""

import os
import time
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our test modules
from .marker_ocr_socks5_proxy import create_gemini_config
from .marker_ocr_http_proxy import create_gemini_config_with_http_proxy
from .proxy_manager import ProxyManager

# Import marker related modules for direct testing
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered

def test_socks5_proxy_with_timing(config, model_name, pdf_path="sample.pdf", max_retries=3):
    """Test SOCKS5 proxy performance, ensure timing starts only after proxy setup is complete"""
    print(f"\n{'='*50}")
    print(f"Testing SOCKS5 Proxy for {model_name}")
    print(f"{'='*50}")
    
    proxy_manager = ProxyManager()
    error_message = "Unknown error occurred"
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"\nAttempt {attempt + 1}...")
            
            # Step 1: Setup SOCKS5 proxy
            print("🔧 Setting up SOCKS5 proxy...")
            setup_start_time = time.time()
            
            nordvpn_proxy = os.getenv('NORDVPN_PROXY')
            if nordvpn_proxy:
                proxy_manager.set_global_proxy(nordvpn_proxy, force=True)
                
                # Test proxy connection
                print("🧪 Testing SOCKS5 proxy connection...")
                if not proxy_manager.test_proxy_connection():
                    print("⚠️ SOCKS5 proxy connection test failed, but continuing")
                else:
                    print("✅ SOCKS5 proxy connection successful")
            
            setup_time = time.time() - setup_start_time
            print(f"⏱️ SOCKS5 proxy setup time: {setup_time:.2f} seconds")
            
            # Step 2: Create configuration and converter (after proxy setup is complete)
            print("🏗️ Creating converter configuration...")
            config_parser = ConfigParser(config)
            converter = PdfConverter(
                config=config_parser.generate_config_dict(),
                artifact_dict=create_model_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer(),
                llm_service=config_parser.get_llm_service()
            )
            
            # Step 3: Start timing (only measure actual PDF processing time)
            print(f"⏰ Starting PDF processing timer at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            start_time = time.time()
            
            # Step 4: Execute OCR conversion
            rendered = converter(pdf_path)
            text, _, images = text_from_rendered(rendered)
            
            # Step 5: Record end time
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✅ PDF processing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️ Pure processing time: {processing_time:.2f} seconds")
            print(f"📝 Text length: {len(text)} characters")
            print(f"🖼️ Image count: {len(images)}")
            
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            from .marker_ocr_socks5_proxy import save_model_output
            output_folder = save_model_output(text, images, model_name, config, timestamp)
            
            return {
                'model_name': model_name,
                'processing_time': processing_time,
                'setup_time': setup_time,
                'total_time': processing_time + setup_time,
                'text_length': len(text),
                'image_count': len(images),
                'text_content': text,
                'images': images,
                'output_folder': output_folder,
                'config': config,
                'success': True,
                'error': None,
                'attempts': attempt + 1,
                'proxy_type': 'SOCKS5'
            }
            
        except Exception as e:
            error_message = str(e)
            print(f"❌ Attempt {attempt + 1} failed: {error_message}")
            
            # Check if it's a geolocation error
            if "FAILED_PRECONDITION" in error_message and "location is not supported" in error_message:
                print("🌍 Detected geolocation restriction error")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    print(f"Waiting {wait_time} seconds before retry...")
                    time.sleep(wait_time)
                    continue
            else:
                break
    
    # All retries failed
    return {
        'model_name': model_name,
        'processing_time': 0,
        'setup_time': 0,
        'total_time': 0,
        'text_length': 0,
        'image_count': 0,
        'text_content': '',
        'images': {},
        'output_folder': None,
        'config': config,
        'success': False,
        'error': error_message,
        'attempts': max_retries,
        'proxy_type': 'SOCKS5'
    }

def test_http_proxy_with_timing(config, model_name, pdf_path="sample.pdf", max_retries=3):
    """Test HTTP proxy performance, ensure timing starts only after SOCKS5->HTTP conversion is complete"""
    print(f"\n{'='*50}")
    print(f"Testing HTTP Proxy (SOCKS5->HTTP conversion) for {model_name}")
    print(f"{'='*50}")
    
    proxy_manager = ProxyManager()
    error_message = "Unknown error occurred"
    
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"\nAttempt {attempt + 1}...")
            
            # Step 1: Setup HTTP proxy (including SOCKS5->HTTP conversion)
            print("🔧 Setting up HTTP proxy (converting from SOCKS5)...")
            setup_start_time = time.time()
            
            nordvpn_proxy = os.getenv('NORDVPN_PROXY')
            if nordvpn_proxy:
                # Execute SOCKS5 to HTTP conversion
                proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=True)
                
                # Test converted proxy connection
                proxy_timeout = config.get('proxy_timeout', 30)
                print(f"🧪 Testing HTTP proxy connection (timeout: {proxy_timeout}s)...")
                if not proxy_manager.test_proxy_connection(timeout=proxy_timeout):
                    print("⚠️ HTTP proxy connection test failed, but continuing")
                else:
                    print("✅ HTTP proxy connection successful")
            
            setup_time = time.time() - setup_start_time
            print(f"⏱️ HTTP proxy setup + conversion time: {setup_time:.2f} seconds")
            
            # Step 2: Create configuration and converter (after proxy conversion is complete)
            print("🏗️ Creating converter configuration...")
            config_parser = ConfigParser(config)
            converter = PdfConverter(
                config=config_parser.generate_config_dict(),
                artifact_dict=create_model_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer(),
                llm_service=config_parser.get_llm_service()
            )
            
            # Step 3: Start timing (only measure actual PDF processing time)
            print(f"⏰ Starting PDF processing timer at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            start_time = time.time()
            
            # Step 4: Execute OCR conversion
            rendered = converter(pdf_path)
            text, _, images = text_from_rendered(rendered)
            
            # Step 5: Record end time
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✅ PDF processing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️ Pure processing time: {processing_time:.2f} seconds")
            print(f"📝 Text length: {len(text)} characters")
            print(f"🖼️ Image count: {len(images)}")
            
            # Save results
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            from .marker_ocr_http_proxy import save_model_output
            output_folder = save_model_output(text, images, model_name, config, timestamp)
            
            return {
                'model_name': model_name,
                'processing_time': processing_time,
                'setup_time': setup_time,
                'total_time': processing_time + setup_time,
                'text_length': len(text),
                'image_count': len(images),
                'text_content': text,
                'images': images,
                'output_folder': output_folder,
                'config': config,
                'success': True,
                'error': None,
                'attempts': attempt + 1,
                'proxy_type': 'HTTP (converted from SOCKS5)'
            }
            
        except Exception as e:
            error_message = str(e)
            print(f"❌ Attempt {attempt + 1} failed: {error_message}")
            
            error_str = error_message.lower()
            
            # Geolocation restriction error
            if "failed_precondition" in error_str and "location is not supported" in error_str:
                print("🌍 Detected geolocation restriction error")
                if attempt < max_retries - 1:
                    print("Will retry with fresh proxy setup...")
                    continue
            
            # Timeout error
            elif any(timeout_keyword in error_str for timeout_keyword in 
                    ['timeout', 'timed out', 'time out', 'connection']):
                print("⏰ Detected timeout error")
                if attempt < max_retries - 1:
                    print("Will retry with extended timeout...")
                    continue
            
            # Other errors
            else:
                print("💥 Non-retryable error detected")
                break
    
    # All retries failed
    return {
        'model_name': model_name,
        'processing_time': 0,
        'setup_time': 0,
        'total_time': 0,
        'text_length': 0,
        'image_count': 0,
        'text_content': '',
        'images': {},
        'output_folder': None,
        'config': config,
        'success': False,
        'error': error_message,
        'attempts': max_retries,
        'proxy_type': 'HTTP (converted from SOCKS5)'
    }

def save_comparison_results(socks5_result, http_result, output_dir="proxy_comparison_results"):
    """Save proxy type comparison results (updated version with detailed timing information)"""
    # Create output directory
    Path(output_dir).mkdir(exist_ok=True)
    
    # Generate timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Create comparison report
    comparison_file = Path(output_dir) / f"proxy_comparison_{timestamp}.md"
    
    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("# Proxy Type Performance Comparison Report\n\n")
        f.write(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("This report compares the pure processing performance of SOCKS5 vs HTTP proxy for Gemini API access.\n")
        f.write("**Important**: Timing excludes proxy setup time to ensure fair comparison.\n\n")
        
        # Detailed performance comparison table
        f.write("## Detailed Performance Comparison\n\n")
        f.write("| Proxy Type | Status | Setup Time(s) | Processing Time(s) | Total Time(s) | Text Length | Images | Retries | Error |\n")
        f.write("|------------|--------|---------------|-------------------|---------------|-------------|---------|---------|-------|\n")
        
        # SOCKS5 results
        socks5_status = "✅ Success" if socks5_result['success'] else "❌ Failed"
        socks5_error = socks5_result.get('error', 'None')[:30] + "..." if socks5_result.get('error') and len(socks5_result.get('error', '')) > 30 else socks5_result.get('error', 'None')
        f.write(f"| SOCKS5 | {socks5_status} | {socks5_result.get('setup_time', 0):.2f} | {socks5_result['processing_time']:.2f} | {socks5_result.get('total_time', 0):.2f} | {socks5_result['text_length']} | {socks5_result['image_count']} | {socks5_result.get('attempts', 1)} | {socks5_error} |\n")
        
        # HTTP results
        http_status = "✅ Success" if http_result['success'] else "❌ Failed"
        http_error = http_result.get('error', 'None')[:30] + "..." if http_result.get('error') and len(http_result.get('error', '')) > 30 else http_result.get('error', 'None')
        f.write(f"| HTTP | {http_status} | {http_result.get('setup_time', 0):.2f} | {http_result['processing_time']:.2f} | {http_result.get('total_time', 0):.2f} | {http_result['text_length']} | {http_result['image_count']} | {http_result.get('attempts', 1)} | {http_error} |\n")
        
        f.write("\n")
        
        # Performance analysis
        f.write("## Performance Analysis\n\n")
        
        if socks5_result['success'] and http_result['success']:
            # Compare pure processing time
            if socks5_result['processing_time'] < http_result['processing_time']:
                faster_proxy = "SOCKS5"
                processing_diff = http_result['processing_time'] - socks5_result['processing_time']
            else:
                faster_proxy = "HTTP"
                processing_diff = socks5_result['processing_time'] - http_result['processing_time']
            
            # Compare total time (including setup time)
            if socks5_result.get('total_time', 0) < http_result.get('total_time', 0):
                faster_total = "SOCKS5"
                total_diff = http_result.get('total_time', 0) - socks5_result.get('total_time', 0)
            else:
                faster_total = "HTTP"
                total_diff = socks5_result.get('total_time', 0) - http_result.get('total_time', 0)
            
            f.write("### ⚡ Processing Performance (Core Comparison)\n\n")
            f.write(f"- **Faster Proxy**: {faster_proxy} is faster by {processing_diff:.2f} seconds\n")
            f.write(f"- **SOCKS5 Processing Time**: {socks5_result['processing_time']:.2f}s\n")
            f.write(f"- **HTTP Processing Time**: {http_result['processing_time']:.2f}s\n")
            f.write(f"- **Performance Difference**: {(processing_diff/min(socks5_result['processing_time'], http_result['processing_time'])*100):.1f}%\n\n")
            
            f.write("### 🔧 Setup Performance (Including Proxy Conversion)\n\n")
            f.write(f"- **SOCKS5 Setup Time**: {socks5_result.get('setup_time', 0):.2f}s\n")
            f.write(f"- **HTTP Setup Time** (includes SOCKS5→HTTP conversion): {http_result.get('setup_time', 0):.2f}s\n")
            f.write(f"- **Setup Overhead**: HTTP proxy requires {http_result.get('setup_time', 0) - socks5_result.get('setup_time', 0):.2f}s additional setup\n\n")
            
            f.write("### 📊 Total Time Comparison\n\n")
            f.write(f"- **Faster Overall**: {faster_total} is faster by {total_diff:.2f} seconds\n")
            f.write(f"- **SOCKS5 Total Time**: {socks5_result.get('total_time', 0):.2f}s\n")
            f.write(f"- **HTTP Total Time**: {http_result.get('total_time', 0):.2f}s\n\n")
            
        # Detailed analysis
        f.write("## Detailed Analysis\n\n")
        
        # SOCKS5 analysis
        f.write("### SOCKS5 Proxy Results\n\n")
        if socks5_result['success']:
            f.write(f"- **Status**: ✅ Successful\n")
            f.write(f"- **Setup Time**: {socks5_result.get('setup_time', 0):.2f} seconds\n")
            f.write(f"- **Processing Time**: {socks5_result['processing_time']:.2f} seconds\n")
            f.write(f"- **Total Time**: {socks5_result.get('total_time', 0):.2f} seconds\n")
            f.write(f"- **Text Extracted**: {socks5_result['text_length']} characters\n")
            f.write(f"- **Images Extracted**: {socks5_result['image_count']}\n")
            f.write(f"- **Retry Attempts**: {socks5_result.get('attempts', 1)}\n")
            if socks5_result.get('output_folder'):
                f.write(f"- **Output Folder**: `{socks5_result['output_folder'].name}`\n")
        else:
            f.write(f"- **Status**: ❌ Failed\n")
            f.write(f"- **Error**: {socks5_result.get('error', 'Unknown error')}\n")
            f.write(f"- **Retry Attempts**: {socks5_result.get('attempts', 1)}\n")
        
        f.write("\n")
        
        # HTTP analysis
        f.write("### HTTP Proxy (SOCKS5→HTTP Conversion) Results\n\n")
        if http_result['success']:
            f.write(f"- **Status**: ✅ Successful\n")
            f.write(f"- **Setup Time**: {http_result.get('setup_time', 0):.2f} seconds (includes conversion)\n")
            f.write(f"- **Processing Time**: {http_result['processing_time']:.2f} seconds\n")
            f.write(f"- **Total Time**: {http_result.get('total_time', 0):.2f} seconds\n")
            f.write(f"- **Text Extracted**: {http_result['text_length']} characters\n")
            f.write(f"- **Images Extracted**: {http_result['image_count']}\n")
            f.write(f"- **Retry Attempts**: {http_result.get('attempts', 1)}\n")
            if http_result.get('output_folder'):
                f.write(f"- **Output Folder**: `{http_result['output_folder'].name}`\n")
        else:
            f.write(f"- **Status**: ❌ Failed\n")
            f.write(f"- **Error**: {http_result.get('error', 'Unknown error')}\n")
            f.write(f"- **Retry Attempts**: {http_result.get('attempts', 1)}\n")
        
        f.write("\n")
        
        # Conclusions and recommendations
        f.write("## Conclusions and Recommendations\n\n")
        
        if socks5_result['success'] and http_result['success']:
            # Determine recommendation
            if socks5_result['processing_time'] < http_result['processing_time']:
                f.write(f"🏆 **SOCKS5 proxy is recommended for better processing performance**\n\n")
                f.write(f"- **Processing advantage**: {http_result['processing_time'] - socks5_result['processing_time']:.2f}s faster\n")
                f.write(f"- **Setup advantage**: {http_result.get('setup_time', 0) - socks5_result.get('setup_time', 0):.2f}s faster setup\n")
            else:
                f.write(f"🏆 **HTTP proxy shows competitive performance despite conversion overhead**\n\n")
                f.write(f"- **Processing time**: Only {socks5_result['processing_time'] - http_result['processing_time']:.2f}s slower\n")
                f.write(f"- **Conversion overhead**: {http_result.get('setup_time', 0) - socks5_result.get('setup_time', 0):.2f}s for SOCKS5→HTTP conversion\n")
            
            f.write(f"- **Reliability**: Both proxies completed successfully\n")
            f.write(f"- **Compatibility**: Both proxy types work with Gemini API\n\n")
            
        elif socks5_result['success'] and not http_result['success']:
            f.write(f"⚠️ **SOCKS5 proxy is the clear choice**\n\n")
            f.write(f"- SOCKS5 proxy successfully accessed Gemini API\n")
            f.write(f"- HTTP proxy conversion failed\n")
            f.write(f"- **Recommendation**: Use SOCKS5 proxy directly\n")
            
        elif not socks5_result['success'] and http_result['success']:
            f.write(f"⚠️ **HTTP proxy (via conversion) is the working option**\n\n")
            f.write(f"- HTTP proxy conversion successfully accessed Gemini API\n")
            f.write(f"- Direct SOCKS5 proxy failed\n")
            f.write(f"- **Recommendation**: Use HTTP proxy via pproxy conversion\n")
            
        else:
            f.write(f"❌ **Both proxy types failed**\n\n")
            f.write(f"- Network or configuration issues detected\n")
            f.write(f"- **Recommendation**: Check proxy credentials and network settings\n")
        
        # Technical details
        f.write("\n## Technical Implementation Details\n\n")
        f.write("### Timing Methodology\n\n")
        f.write("To ensure fair comparison, timing is split into two phases:\n\n")
        f.write("1. **Setup Time**: Proxy configuration and connection establishment\n")
        f.write("   - SOCKS5: Direct proxy setup and testing\n")
        f.write("   - HTTP: SOCKS5→HTTP conversion using pproxy + testing\n\n")
        f.write("2. **Processing Time**: Pure PDF processing (starts after proxy is ready)\n")
        f.write("   - Marker PDF conversion\n")
        f.write("   - Gemini API calls\n")
        f.write("   - Text and image extraction\n\n")
        
        f.write("### Test Configuration\n\n")
        f.write("- **SOCKS5 Proxy**: Direct connection using urllib\n")
        f.write("- **HTTP Proxy**: SOCKS5 converted to HTTP using pproxy\n")
        f.write("- **Test Model**: Gemini 2.5 Flash\n")
        f.write("- **Test Document**: sample.pdf\n")
        f.write("- **Max Retries**: 3 attempts per proxy type\n")
        f.write("- **Timeout Settings**: Extended timeouts for HTTP proxy\n\n")
        
        f.write("### Environment Variables\n\n")
        f.write("```\n")
        f.write(f"NORDVPN_PROXY={os.getenv('NORDVPN_PROXY', 'Not set')}\n")
        f.write(f"GEMINI_API_KEY={'Set' if os.getenv('GEMINI_API_KEY') else 'Not set'}\n")
        f.write(f"GEMINI_MODEL={os.getenv('GEMINI_MODEL', 'Not set')}\n")
        f.write("```\n")
    
    print(f"\n📊 Enhanced comparison report saved to: {comparison_file}")
    return comparison_file

def main():
    """Main comparison test function - enhanced version with accurate timing after proxy setup"""
    print("=" * 60)
    print("🔄 Enhanced Proxy Type Performance Comparison")
    print("=" * 60)
    print("Testing pure processing performance: SOCKS5 vs HTTP proxy")
    print("Timing excludes proxy setup for fair comparison")
    print()
    
    # Check required environment variables
    if not os.getenv('NORDVPN_PROXY'):
        print("❌ NORDVPN_PROXY not found in environment variables")
        return False
    
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    # Create proxy manager
    proxy_manager = ProxyManager()
    
    try:
        # Test 1: SOCKS5 proxy (enhanced version)
        print("🧪 Test 1: SOCKS5 Proxy Performance")
        print("-" * 40)
        
        socks5_config = create_gemini_config()
        socks5_result = test_socks5_proxy_with_timing(
            socks5_config, 
            "Gemini 2.5 Flash (SOCKS5)", 
            pdf_path="sample.pdf"
        )
        
        if socks5_result['success']:
            print(f"✅ SOCKS5 test completed:")
            print(f"   Setup time: {socks5_result.get('setup_time', 0):.2f}s")
            print(f"   Processing time: {socks5_result['processing_time']:.2f}s")
            print(f"   Total time: {socks5_result.get('total_time', 0):.2f}s")
        else:
            print(f"❌ SOCKS5 test failed: {socks5_result.get('error', 'Unknown error')}")
        
        print()
        
        # Test 2: HTTP proxy (enhanced version)
        print("🧪 Test 2: HTTP Proxy Performance (SOCKS5→HTTP Conversion)")
        print("-" * 40)
        
        http_config = create_gemini_config_with_http_proxy()
        http_result = test_http_proxy_with_timing(
            http_config, 
            "Gemini 2.5 Flash (HTTP)", 
            pdf_path="sample.pdf"
        )
        
        if http_result['success']:
            print(f"✅ HTTP test completed:")
            print(f"   Setup time: {http_result.get('setup_time', 0):.2f}s (includes conversion)")
            print(f"   Processing time: {http_result['processing_time']:.2f}s")
            print(f"   Total time: {http_result.get('total_time', 0):.2f}s")
        else:
            print(f"❌ HTTP test failed: {http_result.get('error', 'Unknown error')}")
        
        # Save comparison results
        comparison_file = save_comparison_results(socks5_result, http_result)
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 Performance Test Summary:")
        print(f"  SOCKS5: {'✅ Success' if socks5_result['success'] else '❌ Failed'}")
        if socks5_result['success']:
            print(f"    Processing: {socks5_result['processing_time']:.2f}s")
        
        print(f"  HTTP:   {'✅ Success' if http_result['success'] else '❌ Failed'}")
        if http_result['success']:
            print(f"    Processing: {http_result['processing_time']:.2f}s")
        
        if socks5_result['success'] and http_result['success']:
            processing_diff = abs(socks5_result['processing_time'] - http_result['processing_time'])
            faster = "SOCKS5" if socks5_result['processing_time'] < http_result['processing_time'] else "HTTP"
            print(f"  Winner: {faster} by {processing_diff:.2f}s")
        
        print(f"  Report: {comparison_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False
    
    finally:
        # Clean up proxy settings
        proxy_manager.clear_proxy()
        print("\n🧹 Proxy settings cleared")

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Enhanced proxy comparison test completed successfully!")
    else:
        print("\n❌ Enhanced proxy comparison test failed!")

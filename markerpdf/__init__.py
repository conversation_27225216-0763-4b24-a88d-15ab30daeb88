"""
markerpdf package for unified marker-pdf OCR processing with proxy support

This package provides a unified architecture for OCR processing that eliminates
code duplication while supporting multiple LLM services and proxy configurations.

Key Features:
- Unified OCR interface for all services
- Support for Gemini (with proxy) and OpenRouter (no proxy)
- HTTP and SOCKS5 proxy support with automatic conversion
- Performance comparison tools
- Extensible architecture for future LLM services
"""

# Unified OCR interface (recommended)
from .unified_ocr import (
    create_gemini_config,
    create_gemini_config_with_http_proxy,
    create_openrouter_config,
    test_model,
    test_model_with_retry,
    test_model_http_proxy,
    test_model_with_http_proxy_retry,
    save_model_output
)

# Core OCR functionality
from .core import (
    BaseMarkerOCR,
    save_results_to_markdown
)

# Service factory and implementations
from .services import (
    OCRServiceFactory,
    GeminiOCR,
    OpenRouterOCR,
    ProxyType,
    create_gemini_ocr,
    create_openrouter_ocr,
    create_default_ocr,
    test_gemini_http,
    test_gemini_socks5,
    test_openrouter
)

# Unified proxy comparison (recommended)
from .unified_compare import (
    test_socks5_proxy_with_timing,
    test_http_proxy_with_timing,
    save_comparison_results,
    main as unified_compare_main
)

# Legacy comparison (for backward compatibility)
from .compare_proxy_types import (
    main as legacy_compare_main
)

# Proxy management
from .proxy_manager import (
    ProxyManager,
    setup_early_proxy,
    ensure_proxy_persistence,
    setup_proxy_for_service,
    get_proxy_info,
    validate_proxy_for_service
)

__version__ = "2.0.0"
__author__ = "Brian"

__all__ = [
    # Unified OCR interface (recommended)
    "create_gemini_config",
    "create_gemini_config_with_http_proxy",
    "create_openrouter_config",
    "test_model",
    "test_model_with_retry",
    "test_model_http_proxy",
    "test_model_with_http_proxy_retry",
    "save_model_output",
    "save_results_to_markdown",

    # Core OCR functionality
    "BaseMarkerOCR",

    # Service factory and implementations
    "OCRServiceFactory",
    "GeminiOCR",
    "OpenRouterOCR",
    "ProxyType",
    "create_gemini_ocr",
    "create_openrouter_ocr",
    "create_default_ocr",
    "test_gemini_http",
    "test_gemini_socks5",
    "test_openrouter",

    # Unified proxy comparison (recommended)
    "test_socks5_proxy_with_timing",
    "test_http_proxy_with_timing",
    "save_comparison_results",
    "unified_compare_main",

    # Legacy comparison
    "legacy_compare_main",

    # Proxy management
    "ProxyManager",
    "setup_early_proxy",
    "ensure_proxy_persistence",
    "setup_proxy_for_service",
    "get_proxy_info",
    "validate_proxy_for_service"
]
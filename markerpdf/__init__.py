"""
markerpdf package for marker-pdf OCR processing with proxy support

This package contains modules for testing and comparing different proxy types
(SOCKS5 vs HTTP) with marker-pdf OCR functionality.
"""

from .marker_ocr_socks5_proxy import (
    create_gemini_config,
    create_openrouter_config,
    test_model,
    test_model_with_retry,
    save_model_output,
    save_results_to_markdown
)

from .marker_ocr_http_proxy import (
    create_gemini_config_with_http_proxy,
    test_model_http_proxy,
    test_model_with_http_proxy_retry
)

from .compare_proxy_types import (
    test_socks5_proxy_with_timing,
    test_http_proxy_with_timing,
    save_comparison_results,
    main as compare_main
)

from .proxy_manager import (
    ProxyManager,
    setup_early_proxy,
    ensure_proxy_persistence
)

__version__ = "1.0.0"
__author__ = "Brian"

__all__ = [
    # SOCKS5 proxy functions
    "create_gemini_config",
    "create_openrouter_config", 
    "test_model",
    "test_model_with_retry",
    "save_model_output",
    "save_results_to_markdown",
    
    # HTTP proxy functions
    "create_gemini_config_with_http_proxy",
    "test_model_http_proxy",
    "test_model_with_http_proxy_retry",
    
    # Comparison functions
    "test_socks5_proxy_with_timing",
    "test_http_proxy_with_timing", 
    "save_comparison_results",
    "compare_main",
    
    # Proxy management
    "ProxyManager",
    "setup_early_proxy",
    "ensure_proxy_persistence"
] 
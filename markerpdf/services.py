#!/usr/bin/env python3
"""
LLM service implementations for different providers

This module implements the factory pattern for different LLM services,
providing specific implementations for Gemini (with proxy support) and
OpenRouter (no proxy required) while allowing easy extension for future services.
"""

import os
from enum import Enum
from typing import Dict, Any, Optional, Type

from .core import BaseMarkerOCR
from .config import GeminiConfig, OpenRouterConfig, BaseConfig
from .proxy_manager import ProxyManager


class ProxyType(Enum):
    """代理類型枚舉"""
    NONE = "none"
    HTTP = "http"
    SOCKS5 = "socks5"
    AUTO = "auto"  # 自動檢測並轉換


class GeminiOCR(BaseMarkerOCR):
    """
    Google Gemini OCR 實現
    
    支持 HTTP 和 SOCKS5 代理，默認使用 HTTP 代理以獲得更好的性能。
    """
    
    def __init__(self, proxy_type: ProxyType = ProxyType.HTTP, proxy_timeout: int = 30):
        """
        初始化 Gemini OCR
        
        Args:
            proxy_type: 代理類型
            proxy_timeout: 代理超時時間（秒）
        """
        self.proxy_type = proxy_type
        config = GeminiConfig(proxy_timeout=proxy_timeout)
        super().__init__(config)
    
    def _setup_proxy(self) -> None:
        """設置 Gemini 所需的代理配置"""
        print(f"\n{'='*50}")
        print("Configuring Gemini model (requires proxy)")
        print(f"{'='*50}")
        
        nordvpn_proxy = os.getenv('NORDVPN_PROXY')
        if not nordvpn_proxy:
            print("⚠️ No proxy configured, may not be able to access Gemini API")
            return
        
        # 根據代理類型設置代理
        if self.proxy_type == ProxyType.HTTP:
            print("🔧 Setting up HTTP proxy (converting from SOCKS5 if needed)...")
            self.proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=True)
        elif self.proxy_type == ProxyType.SOCKS5:
            print("🔧 Setting up SOCKS5 proxy...")
            self.proxy_manager.set_global_proxy(nordvpn_proxy, force=True)
        elif self.proxy_type == ProxyType.AUTO:
            print("🔧 Auto-detecting proxy type and setting up...")
            # 默認嘗試 HTTP 代理（更好的性能）
            self.proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=True)
        
        # 測試代理連接
        proxy_timeout = self.config.config.get('proxy_timeout', 30)
        print(f"🧪 Testing proxy connection (timeout: {proxy_timeout}s)...")
        if not self.proxy_manager.test_proxy_connection(timeout=proxy_timeout):
            print("⚠️ Proxy connection test failed, but continuing execution")
        else:
            print("✅ Proxy connection test successful")
    
    def get_model_name(self) -> str:
        """獲取 Gemini 模型名稱"""
        proxy_type_str = self.proxy_type.value.upper()
        if self.proxy_type == ProxyType.HTTP:
            proxy_type_str = "HTTP (converted from SOCKS5)"
        return f"Gemini 2.5 Flash ({proxy_type_str})"


class OpenRouterOCR(BaseMarkerOCR):
    """
    OpenRouter OCR 實現
    
    OpenRouter 不需要代理，可以直接訪問。
    """
    
    def __init__(self):
        """初始化 OpenRouter OCR"""
        config = OpenRouterConfig()
        super().__init__(config)
    
    def _setup_proxy(self) -> None:
        """OpenRouter 不需要代理，清除代理設置"""
        print(f"\n{'='*50}")
        print("Configuring OpenRouter model (clearing proxy)")
        print(f"{'='*50}")
        
        # 清除代理設置
        self.proxy_manager.clear_proxy()
        print("✅ Proxy cleared for OpenRouter access")
    
    def get_model_name(self) -> str:
        """獲取 OpenRouter 模型名稱"""
        model_name = self.config.config.get('openrouter_model_name', 'Deepseek')
        return f"OpenRouter {model_name}"


class OCRServiceFactory:
    """
    OCR 服務工廠類
    
    提供統一的接口來創建不同的 OCR 服務實例。
    """
    
    _services: Dict[str, Type[BaseMarkerOCR]] = {
        'gemini': GeminiOCR,
        'gemini_http': GeminiOCR,
        'gemini_socks5': GeminiOCR,
        'openrouter': OpenRouterOCR,
    }
    
    @classmethod
    def create_service(cls, service_name: str, **kwargs) -> BaseMarkerOCR:
        """
        創建 OCR 服務實例
        
        Args:
            service_name: 服務名稱 ('gemini', 'gemini_http', 'gemini_socks5', 'openrouter')
            **kwargs: 傳遞給服務構造函數的額外參數
            
        Returns:
            OCR 服務實例
            
        Raises:
            ValueError: 如果服務名稱不支持
        """
        if service_name not in cls._services:
            available_services = ', '.join(cls._services.keys())
            raise ValueError(f"Unsupported service: {service_name}. Available: {available_services}")
        
        service_class = cls._services[service_name]
        
        # 為不同的 Gemini 服務類型設置特定參數
        if service_name == 'gemini_http':
            kwargs.setdefault('proxy_type', ProxyType.HTTP)
        elif service_name == 'gemini_socks5':
            kwargs.setdefault('proxy_type', ProxyType.SOCKS5)
        elif service_name == 'gemini':
            # 默認使用 HTTP 代理（更好的性能）
            kwargs.setdefault('proxy_type', ProxyType.HTTP)
        
        return service_class(**kwargs)
    
    @classmethod
    def get_available_services(cls) -> list:
        """獲取可用的服務列表"""
        return list(cls._services.keys())
    
    @classmethod
    def register_service(cls, name: str, service_class: Type[BaseMarkerOCR]) -> None:
        """
        註冊新的 OCR 服務
        
        Args:
            name: 服務名稱
            service_class: 服務類別
        """
        cls._services[name] = service_class
        print(f"✅ Registered new OCR service: {name}")


# 便利函數
def create_gemini_ocr(proxy_type: ProxyType = ProxyType.HTTP, proxy_timeout: int = 30) -> GeminiOCR:
    """
    創建 Gemini OCR 實例
    
    Args:
        proxy_type: 代理類型，默認為 HTTP
        proxy_timeout: 代理超時時間，默認 30 秒
        
    Returns:
        GeminiOCR 實例
    """
    return GeminiOCR(proxy_type=proxy_type, proxy_timeout=proxy_timeout)


def create_openrouter_ocr() -> OpenRouterOCR:
    """
    創建 OpenRouter OCR 實例
    
    Returns:
        OpenRouterOCR 實例
    """
    return OpenRouterOCR()


def create_default_ocr() -> BaseMarkerOCR:
    """
    創建默認 OCR 實例（HTTP 代理 + Gemini）
    
    Returns:
        默認的 OCR 實例
    """
    return create_gemini_ocr(proxy_type=ProxyType.HTTP)


# 向後兼容的配置函數
def create_gemini_config(proxy_type: ProxyType = ProxyType.HTTP, proxy_timeout: int = 30) -> Dict[str, Any]:
    """
    創建 Gemini 配置（向後兼容）
    
    Args:
        proxy_type: 代理類型
        proxy_timeout: 代理超時時間
        
    Returns:
        配置字典
    """
    config = GeminiConfig(proxy_timeout=proxy_timeout)
    config_dict = config.get_config()
    config_dict['proxy_type'] = proxy_type.value
    return config_dict


def create_openrouter_config() -> Dict[str, Any]:
    """
    創建 OpenRouter 配置（向後兼容）
    
    Returns:
        配置字典
    """
    config = OpenRouterConfig()
    return config.get_config()


# 測試函數
def test_gemini_http(pdf_path: str = "sample.pdf") -> Dict[str, Any]:
    """測試 Gemini HTTP 代理"""
    ocr = create_gemini_ocr(proxy_type=ProxyType.HTTP)
    return ocr.test_model_with_retry(pdf_path)


def test_gemini_socks5(pdf_path: str = "sample.pdf") -> Dict[str, Any]:
    """測試 Gemini SOCKS5 代理"""
    ocr = create_gemini_ocr(proxy_type=ProxyType.SOCKS5)
    return ocr.test_model_with_retry(pdf_path)


def test_openrouter(pdf_path: str = "sample.pdf") -> Dict[str, Any]:
    """測試 OpenRouter"""
    ocr = create_openrouter_ocr()
    return ocr.test_model_with_retry(pdf_path)


if __name__ == "__main__":
    print("=== OCR Services Test ===")
    print("Available services:", OCRServiceFactory.get_available_services())
    
    # 測試默認服務（HTTP 代理 + Gemini）
    print("\nTesting default service (HTTP proxy + Gemini)...")
    default_ocr = create_default_ocr()
    result = default_ocr.test_model()
    
    if result['success']:
        print(f"✅ Test successful! Processing time: {result['processing_time']:.2f}s")
        print(f"📁 Output folder: {result['output_folder']}")
    else:
        print(f"❌ Test failed: {result['error']}")
